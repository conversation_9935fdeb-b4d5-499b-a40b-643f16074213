import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { authApi } from '@/api'

export const useUserStore = defineStore('user', () => {
  // 状态
  const user = ref(null)
  const token = ref(localStorage.getItem('token') || '')
  
  // 计算属性
  const isLoggedIn = computed(() => !!token.value)
  const userRole = computed(() => user.value?.role || '')
  const isAdmin = computed(() => userRole.value === 'ADMIN')
  const isOperator = computed(() => userRole.value === 'OPERATOR')
  const isViewer = computed(() => userRole.value === 'VIEWER')
  
  // 方法
  const login = async (credentials) => {
    try {
      console.log('userStore.login called with:', credentials)
      const response = await authApi.login(credentials)
      console.log('authApi.login response:', response)

      if (response.success) {
        token.value = response.token
        user.value = response.user

        // 保存到本地存储
        localStorage.setItem('token', token.value)
        localStorage.setItem('user', JSON.stringify(user.value))

        console.log('登录成功，用户信息已保存')
        return response
      } else {
        console.log('登录失败，响应错误:', response.error)
        throw new Error(response.error || '登录失败')
      }
    } catch (error) {
      console.error('userStore.login error:', error)
      throw error
    }
  }
  
  const logout = () => {
    user.value = null
    token.value = ''
    localStorage.removeItem('token')
    localStorage.removeItem('user')
  }
  
  const getProfile = async () => {
    try {
      const response = await authApi.getProfile()
      if (response.success) {
        user.value = response.user
        localStorage.setItem('user', JSON.stringify(user.value))
      }
      return response
    } catch (error) {
      // 如果获取用户信息失败，清除登录状态
      logout()
      throw error
    }
  }
  
  const initializeAuth = () => {
    const savedUser = localStorage.getItem('user')
    if (savedUser && token.value) {
      try {
        user.value = JSON.parse(savedUser)
      } catch (error) {
        console.error('解析用户信息失败:', error)
        logout()
      }
    }
  }
  
  // 检查权限
  const hasPermission = (permission) => {
    if (!user.value) return false
    
    const rolePermissions = {
      ADMIN: ['user_manage', 'inventory_read', 'inventory_write', 'plan_create', 'plan_approve'],
      OPERATOR: ['inventory_read', 'inventory_write', 'plan_approve'],
      VIEWER: ['inventory_read', 'plan_create']
    }
    
    const userPermissions = rolePermissions[user.value.role] || []
    return userPermissions.includes(permission)
  }
  
  const hasRole = (roles) => {
    if (!user.value) return false
    const allowedRoles = Array.isArray(roles) ? roles : [roles]
    return allowedRoles.includes(user.value.role)
  }
  
  return {
    user,
    token,
    isLoggedIn,
    userRole,
    isAdmin,
    isOperator,
    isViewer,
    login,
    logout,
    getProfile,
    initializeAuth,
    hasPermission,
    hasRole
  }
})
