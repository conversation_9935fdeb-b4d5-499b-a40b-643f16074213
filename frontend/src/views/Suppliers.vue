<template>
  <div class="suppliers-container">
    <el-container>
      <!-- 侧边栏 -->
      <el-aside width="250px" class="sidebar">
        <el-menu
          :default-active="$route.path"
          router
          background-color="#304156"
          text-color="#bfcbd9"
          active-text-color="#409EFF"
        >
          <el-menu-item index="/dashboard">
            <el-icon><House /></el-icon>
            <span>仪表盘</span>
          </el-menu-item>

          <el-sub-menu index="inventory">
            <template #title>
              <el-icon><Box /></el-icon>
              <span>库存管理</span>
            </template>
            <el-menu-item index="/inventory">
              <el-icon><List /></el-icon>
              <span>库存查询</span>
            </el-menu-item>
            <el-menu-item index="/products" v-if="hasPermission(['ADMIN', 'OPERATOR'])">
              <el-icon><Goods /></el-icon>
              <span>商品管理</span>
            </el-menu-item>
            <el-menu-item index="/categories" v-if="hasPermission(['ADMIN'])">
              <el-icon><Menu /></el-icon>
              <span>分类管理</span>
            </el-menu-item>
          </el-sub-menu>

          <el-sub-menu index="business" v-if="hasPermission(['ADMIN', 'OPERATOR'])">
            <template #title>
              <el-icon><Operation /></el-icon>
              <span>业务管理</span>
            </template>
            <el-menu-item index="/suppliers">
              <el-icon><OfficeBuilding /></el-icon>
              <span>供应商管理</span>
            </el-menu-item>
            <el-menu-item index="/warehouses">
              <el-icon><House /></el-icon>
              <span>仓库管理</span>
            </el-menu-item>
          </el-sub-menu>

          <el-menu-item index="/users" v-if="hasPermission(['ADMIN'])">
            <el-icon><User /></el-icon>
            <span>用户管理</span>
          </el-menu-item>
        </el-menu>
      </el-aside>

      <!-- 主内容区 -->
      <el-main>
        <div class="main-content">
          <!-- 页面标题 -->
          <div class="page-header">
            <el-breadcrumb separator="/">
              <el-breadcrumb-item :to="{ path: '/dashboard' }">首页</el-breadcrumb-item>
              <el-breadcrumb-item>业务管理</el-breadcrumb-item>
              <el-breadcrumb-item>供应商管理</el-breadcrumb-item>
            </el-breadcrumb>

            <div class="header-actions">
              <el-dropdown @command="handleUserAction">
                <span class="user-info">
                  <el-icon><User /></el-icon>
                  {{ userStore.user?.username }}
                  <el-icon><ArrowDown /></el-icon>
                </span>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item command="profile">个人资料</el-dropdown-item>
                    <el-dropdown-item command="logout">退出登录</el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </div>
          </div>

          <!-- 供应商管理内容 -->
          <el-card>
            <template #header>
              <div class="card-header">
                <span>供应商管理</span>
                <el-button
                  type="primary"
                  @click="showAddDialog = true"
                  v-if="hasPermission(['ADMIN', 'OPERATOR'])"
                >
                  <el-icon><Plus /></el-icon>
                  添加供应商
                </el-button>
              </div>
            </template>

            <!-- 搜索表单 -->
            <div class="search-form">
              <el-form :model="searchForm" inline>
                <el-form-item label="供应商名称">
                  <el-input
                    v-model="searchForm.name"
                    placeholder="请输入供应商名称"
                    clearable
                    style="width: 200px"
                  />
                </el-form-item>
                <el-form-item label="联系人">
                  <el-input
                    v-model="searchForm.contact"
                    placeholder="请输入联系人"
                    clearable
                    style="width: 150px"
                  />
                </el-form-item>
                <el-form-item label="联系电话">
                  <el-input
                    v-model="searchForm.phone"
                    placeholder="请输入联系电话"
                    clearable
                    style="width: 150px"
                  />
                </el-form-item>
                <el-form-item>
                  <el-button type="primary" @click="loadSuppliersList">
                    <el-icon><Search /></el-icon>
                    搜索
                  </el-button>
                  <el-button @click="resetSearch">
                    <el-icon><Refresh /></el-icon>
                    重置
                  </el-button>
                </el-form-item>
              </el-form>
            </div>

            <!-- 供应商列表 -->
            <el-table
              :data="suppliersList"
              v-loading="loading"
              style="width: 100%"
              stripe
            >
              <el-table-column prop="name" label="供应商名称" min-width="150" />
              <el-table-column prop="contact_person" label="联系人" width="120" />
              <el-table-column prop="phone" label="联系电话" width="130" />
              <el-table-column prop="email" label="邮箱" width="180" />
              <el-table-column prop="address" label="地址" min-width="200" />
              <el-table-column prop="created_at" label="创建时间" width="180">
                <template #default="scope">
                  {{ formatDate(scope.row.created_at) }}
                </template>
              </el-table-column>
              <el-table-column label="操作" width="200" fixed="right" v-if="hasPermission(['ADMIN', 'OPERATOR'])">
                <template #default="scope">
                  <el-button
                    type="primary"
                    size="small"
                    @click="editSupplier(scope.row)"
                  >
                    编辑
                  </el-button>
                  <el-button
                    type="info"
                    size="small"
                    @click="viewProducts(scope.row)"
                  >
                    商品
                  </el-button>
                  <el-button
                    type="danger"
                    size="small"
                    @click="deleteSupplier(scope.row)"
                  >
                    删除
                  </el-button>
                </template>
              </el-table-column>
            </el-table>

            <!-- 分页 -->
            <div class="pagination-container">
              <el-pagination
                v-model:current-page="pagination.page"
                v-model:page-size="pagination.size"
                :page-sizes="[10, 20, 50, 100]"
                :total="pagination.total"
                layout="total, sizes, prev, pager, next, jumper"
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
              />
            </div>
          </el-card>
        </div>
      </el-main>
    </el-container>

    <!-- 添加供应商对话框 -->
    <el-dialog
      v-model="showAddDialog"
      title="添加供应商"
      width="600px"
      @close="resetForm"
    >
      <el-form
        ref="addFormRef"
        :model="addForm"
        :rules="formRules"
        label-width="100px"
      >
        <el-form-item label="供应商名称" prop="name">
          <el-input v-model="addForm.name" placeholder="请输入供应商名称" />
        </el-form-item>
        <el-form-item label="联系人" prop="contact_person">
          <el-input v-model="addForm.contact_person" placeholder="请输入联系人" />
        </el-form-item>
        <el-form-item label="联系电话" prop="phone">
          <el-input v-model="addForm.phone" placeholder="请输入联系电话" />
        </el-form-item>
        <el-form-item label="邮箱" prop="email">
          <el-input v-model="addForm.email" placeholder="请输入邮箱" />
        </el-form-item>
        <el-form-item label="地址" prop="address">
          <el-input
            v-model="addForm.address"
            type="textarea"
            :rows="3"
            placeholder="请输入地址"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="showAddDialog = false">取消</el-button>
          <el-button type="primary" @click="handleAdd">确定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useUserStore } from '@/stores/user'
import { ElMessage, ElMessageBox } from 'element-plus'
import { suppliersApi } from '@/api/index'

const router = useRouter()
const userStore = useUserStore()

// 搜索表单
const searchForm = reactive({
  name: '',
  contact: '',
  phone: ''
})

// 供应商列表
const suppliersList = ref([])
const loading = ref(false)

// 分页
const pagination = reactive({
  page: 1,
  size: 20,
  total: 0
})

// 对话框状态
const showAddDialog = ref(false)
const showEditDialog = ref(false)

// 表单数据
const addForm = reactive({
  name: '',
  contact_person: '',
  phone: '',
  email: '',
  address: ''
})

// 表单验证规则
const formRules = {
  name: [
    { required: true, message: '请输入供应商名称', trigger: 'blur' }
  ],
  contact_person: [
    { required: true, message: '请输入联系人', trigger: 'blur' }
  ],
  phone: [
    { required: true, message: '请输入联系电话', trigger: 'blur' }
  ],
  email: [
    { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' }
  ]
}

const addFormRef = ref()

// 权限检查
const hasPermission = (roles) => {
  if (!userStore.user?.role) return false
  return roles.includes(userStore.user.role)
}

// 用户操作处理
const handleUserAction = (command) => {
  if (command === 'logout') {
    ElMessageBox.confirm('确定要退出登录吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }).then(() => {
      userStore.logout()
      router.push('/login')
    })
  } else if (command === 'profile') {
    router.push('/profile')
  }
}

// 格式化日期
const formatDate = (dateString) => {
  if (!dateString) return ''
  const date = new Date(dateString)
  return date.toLocaleString('zh-CN')
}

// 加载供应商列表
const loadSuppliersList = async () => {
  loading.value = true
  try {
    const params = {
      page: pagination.page,
      limit: pagination.size,
      search: searchForm.name || searchForm.contact || searchForm.phone
    }

    const response = await suppliersApi.getList(params)

    if (response.success) {
      suppliersList.value = response.data
      pagination.total = response.pagination?.total || 0
    } else {
      ElMessage.error(response.error || '获取供应商列表失败')
    }
  } catch (error) {
    console.error('加载供应商列表失败:', error)
    ElMessage.error('加载供应商列表失败')
  } finally {
    loading.value = false
  }
}

// 重置搜索
const resetSearch = () => {
  searchForm.name = ''
  searchForm.contact = ''
  searchForm.phone = ''
  pagination.page = 1
  loadSuppliersList()
}

// 分页处理
const handleSizeChange = (size) => {
  pagination.size = size
  pagination.page = 1
  loadSuppliersList()
}

const handleCurrentChange = (page) => {
  pagination.page = page
  loadSuppliersList()
}

// 重置表单
const resetForm = () => {
  addForm.name = ''
  addForm.contact_person = ''
  addForm.phone = ''
  addForm.email = ''
  addForm.address = ''
  if (addFormRef.value) {
    addFormRef.value.clearValidate()
  }
}

// 添加供应商
const handleAdd = async () => {
  if (!addFormRef.value) return

  try {
    await addFormRef.value.validate()

    const response = await suppliersApi.create(addForm)
    if (response.success) {
      ElMessage.success('添加成功')
      showAddDialog.value = false
      resetForm()
      loadSuppliersList()
    } else {
      ElMessage.error(response.error || '添加失败')
    }
  } catch (error) {
    console.error('添加供应商失败:', error)
    ElMessage.error('添加失败')
  }
}

// 编辑供应商
const editSupplier = (supplier) => {
  ElMessage.info('编辑功能开发中...')
}

// 查看供应商商品
const viewProducts = (supplier) => {
  ElMessage.info('查看商品功能开发中...')
}

// 删除供应商
const deleteSupplier = (supplier) => {
  ElMessageBox.confirm(
    `确定要删除供应商 "${supplier.name}" 吗？`,
    '删除确认',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(async () => {
    try {
      const response = await suppliersApi.delete(supplier.id)
      if (response.success) {
        ElMessage.success('删除成功')
        loadSuppliersList()
      } else {
        ElMessage.error(response.error || '删除失败')
      }
    } catch (error) {
      console.error('删除供应商失败:', error)
      ElMessage.error('删除失败')
    }
  })
}

onMounted(() => {
  loadSuppliersList()
})
</script>

<style scoped>
.suppliers-container {
  display: flex;
  height: 100vh;
}

.sidebar {
  background-color: #304156;
  color: white;
}

.main-content {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 10px;
  border-bottom: 1px solid #ebeef5;
}

.user-info {
  display: flex;
  align-items: center;
  cursor: pointer;
  color: #606266;
}

.user-info .el-icon {
  margin: 0 5px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.search-form {
  margin-bottom: 20px;
  padding: 20px;
  background-color: #f5f7fa;
  border-radius: 4px;
}

.pagination-container {
  margin-top: 20px;
  text-align: right;
}

.el-table {
  margin-top: 20px;
}

.dialog-footer {
  text-align: right;
}
</style>
