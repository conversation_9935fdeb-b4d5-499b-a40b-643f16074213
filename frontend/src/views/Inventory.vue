<template>
  <div class="inventory-container">
    <!-- 侧边栏 -->
    <el-aside width="250px" class="sidebar">
      <div class="logo-container">
        <h2>仓库管理系统</h2>
      </div>
      <el-menu
        :default-active="$route.path"
        class="sidebar-menu"
        router
        unique-opened
      >
        <el-menu-item index="/dashboard">
          <el-icon><House /></el-icon>
          <span>仪表盘</span>
        </el-menu-item>
        
        <el-sub-menu index="inventory">
          <template #title>
            <el-icon><Box /></el-icon>
            <span>库存管理</span>
          </template>
          <el-menu-item index="/inventory">
            <el-icon><List /></el-icon>
            <span>库存查询</span>
          </el-menu-item>
          <el-menu-item index="/products" v-if="hasPermission(['ADMIN', 'OPERATOR'])">
            <el-icon><Goods /></el-icon>
            <span>商品管理</span>
          </el-menu-item>
          <el-menu-item index="/categories" v-if="hasPermission(['ADMIN'])">
            <el-icon><Menu /></el-icon>
            <span>分类管理</span>
          </el-menu-item>
        </el-sub-menu>
        
        <el-sub-menu index="business" v-if="hasPermission(['ADMIN', 'OPERATOR'])">
          <template #title>
            <el-icon><OfficeBuilding /></el-icon>
            <span>业务管理</span>
          </template>
          <el-menu-item index="/suppliers">
            <el-icon><User /></el-icon>
            <span>供应商管理</span>
          </el-menu-item>
          <el-menu-item index="/warehouses" v-if="hasPermission(['ADMIN'])">
            <el-icon><School /></el-icon>
            <span>仓库管理</span>
          </el-menu-item>
          <el-menu-item index="/demand-plans">
            <el-icon><Document /></el-icon>
            <span>需求计划</span>
          </el-menu-item>
        </el-sub-menu>
        
        <el-menu-item index="/reports">
          <el-icon><DataAnalysis /></el-icon>
          <span>报表统计</span>
        </el-menu-item>
        
        <el-menu-item index="/users" v-if="hasPermission(['ADMIN'])">
          <el-icon><UserFilled /></el-icon>
          <span>用户管理</span>
        </el-menu-item>
      </el-menu>
    </el-aside>

    <!-- 主内容区 -->
    <el-container class="main-container">
      <!-- 顶部导航 -->
      <el-header class="header">
        <div class="header-left">
          <el-breadcrumb separator="/">
            <el-breadcrumb-item>首页</el-breadcrumb-item>
            <el-breadcrumb-item>库存管理</el-breadcrumb-item>
            <el-breadcrumb-item>库存查询</el-breadcrumb-item>
          </el-breadcrumb>
        </div>
        <div class="header-right">
          <el-dropdown @command="handleCommand">
            <span class="user-info">
              <el-icon><Avatar /></el-icon>
              {{ userStore.user?.username }}
              <el-icon class="el-icon--right"><ArrowDown /></el-icon>
            </span>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item command="profile">
                  <el-icon><User /></el-icon>
                  个人资料
                </el-dropdown-item>
                <el-dropdown-item command="logout" divided>
                  <el-icon><SwitchButton /></el-icon>
                  退出登录
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>
      </el-header>

      <!-- 主要内容 -->
      <el-main class="main-content">
        <div class="inventory-content">
          <!-- 搜索区域 -->
          <el-card class="search-card">
            <el-form :model="searchForm" inline>
              <el-form-item label="商品编码">
                <el-input v-model="searchForm.code" placeholder="请输入商品编码" clearable />
              </el-form-item>
              <el-form-item label="商品名称">
                <el-input v-model="searchForm.name" placeholder="请输入商品名称" clearable />
              </el-form-item>
              <el-form-item label="仓库">
                <el-select v-model="searchForm.warehouseId" placeholder="请选择仓库" clearable>
                  <el-option
                    v-for="warehouse in warehouses"
                    :key="warehouse.id"
                    :label="warehouse.name"
                    :value="warehouse.id"
                  />
                </el-select>
              </el-form-item>
              <el-form-item label="分类">
                <el-select v-model="searchForm.categoryId" placeholder="请选择分类" clearable>
                  <el-option
                    v-for="category in categories"
                    :key="category.id"
                    :label="category.name"
                    :value="category.id"
                  />
                </el-select>
              </el-form-item>
              <el-form-item>
                <el-button type="primary" @click="handleSearch">
                  <el-icon><Search /></el-icon>
                  搜索
                </el-button>
                <el-button @click="handleReset">
                  <el-icon><Refresh /></el-icon>
                  重置
                </el-button>
              </el-form-item>
            </el-form>
          </el-card>

          <!-- 库存列表 -->
          <el-card style="margin-top: 20px;">
            <template #header>
              <div class="card-header">
                <span>库存列表</span>
                <div>
                  <el-button type="success" @click="handleExport">
                    <el-icon><Download /></el-icon>
                    导出
                  </el-button>
                </div>
              </div>
            </template>
            
            <el-table
              :data="inventoryList"
              v-loading="loading"
              style="width: 100%"
              stripe
            >
              <el-table-column prop="product_code" label="商品编码" width="120" />
              <el-table-column prop="product_name" label="商品名称" min-width="150" />
              <el-table-column prop="warehouse_name" label="仓库" width="120" />
              <el-table-column prop="category_name" label="分类" width="100" />
              <el-table-column prop="unit" label="单位" width="80" />
              <el-table-column prop="quantity" label="库存数量" width="100" align="right">
                <template #default="scope">
                  <span :class="getStockClass(scope.row)">
                    {{ scope.row.quantity }}
                  </span>
                </template>
              </el-table-column>
              <el-table-column prop="reserved_quantity" label="预留数量" width="100" align="right" />
              <el-table-column prop="available_quantity" label="可用数量" width="100" align="right">
                <template #default="scope">
                  {{ scope.row.quantity - scope.row.reserved_quantity }}
                </template>
              </el-table-column>
              <el-table-column prop="min_stock" label="最小库存" width="100" align="right" />
              <el-table-column prop="location" label="存放位置" width="120" />
              <el-table-column prop="last_updated" label="最后更新" width="160">
                <template #default="scope">
                  {{ formatDateTime(scope.row.last_updated) }}
                </template>
              </el-table-column>
              <el-table-column label="状态" width="80">
                <template #default="scope">
                  <el-tag :type="getStatusType(scope.row)">
                    {{ getStatusText(scope.row) }}
                  </el-tag>
                </template>
              </el-table-column>
            </el-table>

            <!-- 分页 -->
            <div class="pagination-container">
              <el-pagination
                v-model:current-page="pagination.page"
                v-model:page-size="pagination.size"
                :page-sizes="[10, 20, 50, 100]"
                :total="pagination.total"
                layout="total, sizes, prev, pager, next, jumper"
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
              />
            </div>
          </el-card>
        </div>
      </el-main>
    </el-container>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useUserStore } from '@/stores/user'
import { ElMessage, ElMessageBox } from 'element-plus'
import { inventoryApi } from '@/api/index'

const router = useRouter()
const userStore = useUserStore()

// 搜索表单
const searchForm = reactive({
  code: '',
  name: '',
  warehouseId: '',
  categoryId: ''
})

// 库存列表
const inventoryList = ref([])
const loading = ref(false)

// 仓库列表
const warehouses = ref([])

// 分类列表
const categories = ref([])

// 分页
const pagination = reactive({
  page: 1,
  size: 20,
  total: 0
})

// 权限检查
const hasPermission = (roles) => {
  if (!userStore.user?.role) return false
  return roles.includes(userStore.user.role)
}

// 格式化日期时间
const formatDateTime = (dateTime) => {
  return new Date(dateTime).toLocaleString('zh-CN')
}

// 获取库存状态样式
const getStockClass = (row) => {
  if (row.quantity <= 0) return 'stock-out'
  if (row.quantity <= row.min_stock) return 'stock-low'
  return 'stock-normal'
}

// 获取状态类型
const getStatusType = (row) => {
  if (row.quantity <= 0) return 'danger'
  if (row.quantity <= row.min_stock) return 'warning'
  return 'success'
}

// 获取状态文本
const getStatusText = (row) => {
  if (row.quantity <= 0) return '缺货'
  if (row.quantity <= row.min_stock) return '预警'
  return '正常'
}

// 处理搜索
const handleSearch = () => {
  pagination.page = 1
  loadInventoryList()
}

// 处理重置
const handleReset = () => {
  Object.keys(searchForm).forEach(key => {
    searchForm[key] = ''
  })
  pagination.page = 1
  loadInventoryList()
}

// 处理导出
const handleExport = () => {
  ElMessage.info('导出功能开发中...')
}

// 处理页码变化
const handleCurrentChange = (page) => {
  pagination.page = page
  loadInventoryList()
}

// 处理页大小变化
const handleSizeChange = (size) => {
  pagination.size = size
  pagination.page = 1
  loadInventoryList()
}

// 处理用户菜单命令
const handleCommand = async (command) => {
  switch (command) {
    case 'profile':
      router.push('/profile')
      break
    case 'logout':
      try {
        await ElMessageBox.confirm('确定要退出登录吗？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
        await userStore.logout()
        router.push('/login')
        ElMessage.success('退出登录成功')
      } catch (error) {
        // 用户取消操作
      }
      break
  }
}

// 加载库存列表
const loadInventoryList = async () => {
  loading.value = true
  try {
    const params = {
      page: pagination.page,
      limit: pagination.size,
      search: searchForm.name || searchForm.code,
      category: searchForm.categoryId,
      warehouse: searchForm.warehouseId
    }

    const response = await inventoryApi.getList(params)

    if (response.success) {
      inventoryList.value = response.data.map(item => ({
        id: item.id,
        product_code: item.code,
        product_name: item.name,
        warehouse_name: '主仓库', // 暂时硬编码，后续从warehouse表获取
        category_name: '工具', // 暂时硬编码，后续从category表获取
        unit: item.unit,
        quantity: item.total_quantity,
        reserved_quantity: item.reserved_quantity,
        available_quantity: item.available_quantity,
        min_stock: item.min_stock,
        location: item.location,
        last_updated: new Date().toISOString()
      }))

      pagination.total = response.pagination?.total || 0
    } else {
      ElMessage.error(response.error || '获取库存数据失败')
    }
  } catch (error) {
    console.error('加载库存列表失败:', error)
    ElMessage.error('加载库存列表失败')
  } finally {
    loading.value = false
  }
}

// 加载仓库列表
const loadWarehouses = async () => {
  try {
    warehouses.value = [
      { id: 1, name: '主仓库' },
      { id: 2, name: '备用仓库' }
    ]
  } catch (error) {
    console.error('加载仓库列表失败:', error)
  }
}

// 加载分类列表
const loadCategories = async () => {
  try {
    categories.value = [
      { id: 1, name: '工具' },
      { id: 2, name: '电动工具' },
      { id: 3, name: '配件' }
    ]
  } catch (error) {
    console.error('加载分类列表失败:', error)
  }
}

onMounted(() => {
  loadInventoryList()
  loadWarehouses()
  loadCategories()
})
</script>

<style scoped>
.inventory-container {
  display: flex;
  height: 100vh;
}

.sidebar {
  background-color: #304156;
  color: white;
}

.logo-container {
  padding: 20px;
  text-align: center;
  border-bottom: 1px solid #434a50;
}

.logo-container h2 {
  color: white;
  margin: 0;
  font-size: 18px;
}

.sidebar-menu {
  background-color: #304156;
  border: none;
}

.sidebar-menu .el-menu-item {
  color: #bfcbd9;
}

.sidebar-menu .el-menu-item:hover {
  background-color: #263445 !important;
  color: #409eff !important;
}

.sidebar-menu .el-menu-item.is-active {
  background-color: #409eff !important;
  color: white !important;
}

.sidebar-menu .el-sub-menu__title {
  color: #bfcbd9;
}

.sidebar-menu .el-sub-menu__title:hover {
  background-color: #263445 !important;
  color: #409eff !important;
}

.main-container {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.header {
  background-color: white;
  border-bottom: 1px solid #e6e6e6;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 20px;
}

.header-left {
  flex: 1;
}

.header-right {
  display: flex;
  align-items: center;
}

.user-info {
  display: flex;
  align-items: center;
  cursor: pointer;
  padding: 8px 12px;
  border-radius: 4px;
  transition: background-color 0.3s;
}

.user-info:hover {
  background-color: #f5f7fa;
}

.main-content {
  background-color: #f0f2f5;
  padding: 20px;
  overflow-y: auto;
}

.inventory-content {
  max-width: 1400px;
  margin: 0 auto;
}

.search-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.pagination-container {
  margin-top: 20px;
  text-align: right;
}

.stock-out {
  color: #f56c6c;
  font-weight: bold;
}

.stock-low {
  color: #e6a23c;
  font-weight: bold;
}

.stock-normal {
  color: #67c23a;
}
</style>
