<template>
  <div class="categories-container">
    <el-container>
      <!-- 侧边栏 -->
      <el-aside width="250px" class="sidebar">
        <el-menu
          :default-active="$route.path"
          router
          background-color="#304156"
          text-color="#bfcbd9"
          active-text-color="#409EFF"
        >
          <el-menu-item index="/dashboard">
            <el-icon><House /></el-icon>
            <span>仪表盘</span>
          </el-menu-item>

          <el-sub-menu index="inventory">
            <template #title>
              <el-icon><Box /></el-icon>
              <span>库存管理</span>
            </template>
            <el-menu-item index="/inventory">
              <el-icon><List /></el-icon>
              <span>库存查询</span>
            </el-menu-item>
            <el-menu-item index="/products" v-if="hasPermission(['ADMIN', 'OPERATOR'])">
              <el-icon><Goods /></el-icon>
              <span>商品管理</span>
            </el-menu-item>
            <el-menu-item index="/categories" v-if="hasPermission(['ADMIN'])">
              <el-icon><Menu /></el-icon>
              <span>分类管理</span>
            </el-menu-item>
          </el-sub-menu>

          <el-sub-menu index="business" v-if="hasPermission(['ADMIN', 'OPERATOR'])">
            <template #title>
              <el-icon><Operation /></el-icon>
              <span>业务管理</span>
            </template>
            <el-menu-item index="/suppliers">
              <el-icon><OfficeBuilding /></el-icon>
              <span>供应商管理</span>
            </el-menu-item>
            <el-menu-item index="/warehouses">
              <el-icon><House /></el-icon>
              <span>仓库管理</span>
            </el-menu-item>
          </el-sub-menu>

          <el-menu-item index="/users" v-if="hasPermission(['ADMIN'])">
            <el-icon><User /></el-icon>
            <span>用户管理</span>
          </el-menu-item>
        </el-menu>
      </el-aside>

      <!-- 主内容区 -->
      <el-main>
        <div class="main-content">
          <!-- 页面标题 -->
          <div class="page-header">
            <el-breadcrumb separator="/">
              <el-breadcrumb-item :to="{ path: '/dashboard' }">首页</el-breadcrumb-item>
              <el-breadcrumb-item>库存管理</el-breadcrumb-item>
              <el-breadcrumb-item>分类管理</el-breadcrumb-item>
            </el-breadcrumb>

            <div class="header-actions">
              <el-dropdown @command="handleUserAction">
                <span class="user-info">
                  <el-icon><User /></el-icon>
                  {{ userStore.user?.username }}
                  <el-icon><ArrowDown /></el-icon>
                </span>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item command="profile">个人资料</el-dropdown-item>
                    <el-dropdown-item command="logout">退出登录</el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </div>
          </div>

          <!-- 分类管理内容 -->
          <el-card>
            <template #header>
              <div class="card-header">
                <span>分类管理</span>
                <el-button
                  type="primary"
                  @click="showAddDialog = true"
                  v-if="hasPermission(['ADMIN'])"
                >
                  <el-icon><Plus /></el-icon>
                  添加分类
                </el-button>
              </div>
            </template>

            <!-- 分类列表 -->
            <el-table
              :data="categoriesList"
              v-loading="loading"
              style="width: 100%"
              stripe
              row-key="id"
              :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
            >
              <el-table-column prop="name" label="分类名称" min-width="200" />
              <el-table-column prop="level" label="层级" width="80" align="center" />
              <el-table-column prop="sort_order" label="排序" width="80" align="center" />
              <el-table-column prop="created_at" label="创建时间" width="180">
                <template #default="scope">
                  {{ formatDate(scope.row.created_at) }}
                </template>
              </el-table-column>
              <el-table-column label="操作" width="200" fixed="right" v-if="hasPermission(['ADMIN'])">
                <template #default="scope">
                  <el-button
                    type="primary"
                    size="small"
                    @click="editCategory(scope.row)"
                  >
                    编辑
                  </el-button>
                  <el-button
                    type="danger"
                    size="small"
                    @click="deleteCategory(scope.row)"
                  >
                    删除
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
          </el-card>
        </div>
      </el-main>
    </el-container>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useUserStore } from '@/stores/user'
import { ElMessage, ElMessageBox } from 'element-plus'
import { categoriesApi } from '@/api/index'

const router = useRouter()
const userStore = useUserStore()

// 分类列表
const categoriesList = ref([])
const loading = ref(false)

// 对话框状态
const showAddDialog = ref(false)
const showEditDialog = ref(false)
const currentCategory = ref({})

// 权限检查
const hasPermission = (roles) => {
  if (!userStore.user?.role) return false
  return roles.includes(userStore.user.role)
}

// 用户操作处理
const handleUserAction = (command) => {
  if (command === 'logout') {
    ElMessageBox.confirm('确定要退出登录吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }).then(() => {
      userStore.logout()
      router.push('/login')
    })
  } else if (command === 'profile') {
    router.push('/profile')
  }
}

// 格式化日期
const formatDate = (dateString) => {
  if (!dateString) return ''
  const date = new Date(dateString)
  return date.toLocaleString('zh-CN')
}

// 加载分类列表
const loadCategoriesList = async () => {
  loading.value = true
  try {
    const response = await categoriesApi.getList({ limit: 100 })

    if (response.success) {
      categoriesList.value = response.data
    } else {
      ElMessage.error(response.error || '获取分类列表失败')
    }
  } catch (error) {
    console.error('加载分类列表失败:', error)
    ElMessage.error('加载分类列表失败')
  } finally {
    loading.value = false
  }
}

// 编辑分类
const editCategory = (category) => {
  currentCategory.value = { ...category }
  showEditDialog.value = true
}

// 删除分类
const deleteCategory = (category) => {
  ElMessageBox.confirm(
    `确定要删除分类 "${category.name}" 吗？`,
    '删除确认',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(async () => {
    try {
      const response = await categoriesApi.delete(category.id)
      if (response.success) {
        ElMessage.success('删除成功')
        loadCategoriesList()
      } else {
        ElMessage.error(response.error || '删除失败')
      }
    } catch (error) {
      console.error('删除分类失败:', error)
      ElMessage.error('删除失败')
    }
  })
}

onMounted(() => {
  loadCategoriesList()
})
</script>

<style scoped>
.categories-container {
  display: flex;
  height: 100vh;
}

.sidebar {
  background-color: #304156;
  color: white;
}

.main-content {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 10px;
  border-bottom: 1px solid #ebeef5;
}

.user-info {
  display: flex;
  align-items: center;
  cursor: pointer;
  color: #606266;
}

.user-info .el-icon {
  margin: 0 5px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.el-table {
  margin-top: 20px;
}
</style>
