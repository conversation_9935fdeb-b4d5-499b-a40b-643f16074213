import { createRouter, createWebHistory } from 'vue-router'

// 路由组件懒加载
const Login = () => import('@/views/Login.vue')
const Dashboard = () => import('@/views/Dashboard.vue')
const Inventory = () => import('@/views/Inventory.vue')
const Products = () => import('@/views/Products.vue')
const Categories = () => import('@/views/Categories.vue')
const Suppliers = () => import('@/views/Suppliers.vue')
const Warehouses = () => import('@/views/Warehouses.vue')
const DemandPlans = () => import('@/views/DemandPlans.vue')
const Reports = () => import('@/views/Reports.vue')
const UserManagement = () => import('@/views/UserManagement.vue')
const Profile = () => import('@/views/Profile.vue')
const NotFound = () => import('@/views/NotFound.vue')
const Test = () => import('@/views/Test.vue')

const routes = [
  {
    path: '/',
    redirect: '/login'
  },
  {
    path: '/test',
    name: 'Test',
    component: Test,
    meta: {
      requiresAuth: false,
      title: '测试页面'
    }
  },
  {
    path: '/login',
    name: 'Login',
    component: Login,
    meta: {
      requiresAuth: false,
      title: '登录'
    }
  },
  {
    path: '/dashboard',
    name: 'Dashboard',
    component: Dashboard,
    meta: { 
      requiresAuth: true,
      title: '仪表盘'
    }
  },
  {
    path: '/inventory',
    name: 'Inventory',
    component: Inventory,
    meta: { 
      requiresAuth: true,
      title: '库存管理',
      roles: ['ADMIN', 'OPERATOR', 'VIEWER']
    }
  },
  {
    path: '/products',
    name: 'Products',
    component: Products,
    meta: { 
      requiresAuth: true,
      title: '商品管理',
      roles: ['ADMIN', 'OPERATOR']
    }
  },
  {
    path: '/categories',
    name: 'Categories',
    component: Categories,
    meta: { 
      requiresAuth: true,
      title: '分类管理',
      roles: ['ADMIN']
    }
  },
  {
    path: '/suppliers',
    name: 'Suppliers',
    component: Suppliers,
    meta: { 
      requiresAuth: true,
      title: '供应商管理',
      roles: ['ADMIN', 'OPERATOR']
    }
  },
  {
    path: '/warehouses',
    name: 'Warehouses',
    component: Warehouses,
    meta: { 
      requiresAuth: true,
      title: '仓库管理',
      roles: ['ADMIN']
    }
  },
  {
    path: '/demand-plans',
    name: 'DemandPlans',
    component: DemandPlans,
    meta: { 
      requiresAuth: true,
      title: '需求计划',
      roles: ['ADMIN', 'OPERATOR']
    }
  },
  {
    path: '/reports',
    name: 'Reports',
    component: Reports,
    meta: { 
      requiresAuth: true,
      title: '报表统计',
      roles: ['ADMIN', 'OPERATOR', 'VIEWER']
    }
  },
  {
    path: '/users',
    name: 'UserManagement',
    component: UserManagement,
    meta: { 
      requiresAuth: true,
      title: '用户管理',
      roles: ['ADMIN']
    }
  },
  {
    path: '/profile',
    name: 'Profile',
    component: Profile,
    meta: { 
      requiresAuth: true,
      title: '个人资料'
    }
  },
  {
    path: '/404',
    name: 'NotFound',
    component: NotFound,
    meta: { 
      title: '页面未找到'
    }
  },
  {
    path: '/:pathMatch(.*)*',
    redirect: '/404'
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes,
  scrollBehavior(to, from, savedPosition) {
    if (savedPosition) {
      return savedPosition
    } else {
      return { top: 0 }
    }
  }
})

// 路由守卫
router.beforeEach(async (to, from, next) => {
  // 设置页面标题
  if (to.meta.title) {
    document.title = `${to.meta.title} - 武汉总厂仓库管理系统`
  }

  // 检查是否需要认证
  if (to.meta.requiresAuth) {
    // 检查本地存储中的token
    const token = localStorage.getItem('token')
    if (!token) {
      // 未登录，重定向到登录页
      next({
        path: '/login',
        query: { redirect: to.fullPath }
      })
      return
    }

    // 检查角色权限
    if (to.meta.roles) {
      const userStr = localStorage.getItem('user')
      if (userStr) {
        try {
          const user = JSON.parse(userStr)
          if (!to.meta.roles.includes(user.role)) {
            // 权限不足，重定向到仪表盘
            next('/dashboard')
            return
          }
        } catch (error) {
          // 用户信息解析失败，重定向到登录页
          next('/login')
          return
        }
      }
    }
  } else if (to.path === '/login') {
    // 检查是否已登录
    const token = localStorage.getItem('token')
    if (token) {
      // 已登录用户访问登录页，重定向到仪表盘
      next('/dashboard')
      return
    }
  }

  next()
})

export default router
