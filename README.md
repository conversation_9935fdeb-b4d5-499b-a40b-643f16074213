# 武汉总厂仓库管理系统

一个基于Vue3 + Express.js的现代化仓库管理系统，支持库存管理、需求计划、用户权限管理等功能。

## 🚀 技术栈

### 前端
- **Vue 3** - 渐进式JavaScript框架
- **Vite** - 快速构建工具
- **Element Plus** - Vue 3 UI组件库
- **Pinia** - Vue状态管理
- **Vue Router** - 路由管理
- **Axios** - HTTP客户端

### 后端
- **Node.js** - JavaScript运行环境
- **Express.js** - Web应用框架
- **MySQL** - 关系型数据库
- **JWT** - 身份验证
- **bcryptjs** - 密码加密

## 📦 项目结构

```
wms-system/
├── frontend/                 # Vue3前端
│   ├── src/
│   │   ├── api/             # API接口
│   │   ├── components/      # 可复用组件
│   │   ├── views/          # 页面视图
│   │   ├── router/         # 路由配置
│   │   ├── stores/         # 状态管理
│   │   └── styles/         # 样式文件
│   └── package.json
├── backend/                 # Express.js后端
│   ├── routes/             # 路由文件
│   ├── middleware/         # 中间件
│   ├── config/             # 配置文件
│   ├── app.js              # 应用入口
│   └── package.json
├── database/               # 数据库脚本
│   └── init.sql            # 初始化脚本
└── README.md
```

## 🛠️ 安装与运行

### 环境要求
- Node.js 18+
- MySQL 8.0+
- Git

### 1. 克隆项目
```bash
git clone <repository-url>
cd wms-system
```

### 2. 数据库配置
```bash
# 登录MySQL
mysql -u root -p

# 执行初始化脚本
source database/init.sql
```

### 3. 后端配置
```bash
cd backend

# 安装依赖
npm install

# 配置环境变量（已创建.env文件）
# 根据实际情况修改数据库连接信息

# 启动开发服务器
npm run dev
```

### 4. 前端配置
```bash
cd frontend

# 安装依赖
npm install

# 启动开发服务器
npm run dev
```

### 5. 访问系统
- 前端地址: http://localhost:3000
- 后端API: http://localhost:3001
- 默认管理员账户: admin / 123456

## 🔧 配置说明

### 数据库配置
在 `backend/.env` 文件中配置数据库连接：
```env
DB_HOST=localhost
DB_PORT=3306
DB_NAME=wms_617
DB_USER=root
DB_PASSWORD=179100215
```

### 邮件服务配置
```env
SMTP_SERVER=smtp.qq.com
SMTP_PORT=465
SENDER_EMAIL=<EMAIL>
SENDER_PASSWORD=vfnlrtrgrpixcaee
```

## 👥 用户角色权限

### 管理员 (ADMIN)
- 用户管理：增删改查用户
- 库存管理：所有库存操作
- 需求计划：审批需求计划
- 系统管理：系统设置、备份等

### 操作员 (OPERATOR)
- 库存管理：入库、出库操作
- 需求计划：审批需求计划
- 查看库存信息

### 维修负责人 (VIEWER)
- 查看库存信息
- 提交需求计划
- 查看自己的需求计划

## 📋 主要功能

### 🏠 主页仪表盘
- 库存统计图表
- 低库存预警
- 最近操作记录
- 系统公告

### 📦 库存管理
- 商品信息管理
- 入库/出库操作
- 库存查询统计
- 库存盘点功能

### 📝 需求计划
- 需求计划提交
- 审批流程管理
- Excel导入导出
- 计划执行跟踪

### 👤 用户管理
- 用户账户管理
- 角色权限分配
- 操作日志记录

### ⚙️ 系统管理
- 基础数据维护
- 系统参数配置
- 数据备份恢复
- 操作日志查看

## 🎨 UI设计特色

- **现代化设计**: 采用渐变蓝色主题，界面简洁美观
- **响应式布局**: 支持PC端和移动端访问
- **交互友好**: 丰富的动画效果和用户反馈
- **组件化开发**: 可复用的UI组件，提高开发效率

## 🔒 安全特性

- JWT身份验证
- 密码加密存储
- 权限控制中间件
- SQL注入防护
- XSS攻击防护

## 📊 性能优化

- 前端代码分割
- 图片懒加载
- API请求缓存
- 数据库索引优化
- 连接池管理

## 🚀 部署说明

### 开发环境
```bash
# 前端
cd frontend && npm run dev

# 后端
cd backend && npm run dev
```

### 生产环境
```bash
# 前端构建
cd frontend && npm run build

# 后端启动
cd backend && npm start

# 使用PM2管理进程
pm2 start backend/app.js --name wms-backend
```

## 📝 开发规范

### Git提交规范
- `feat`: 新功能
- `fix`: 修复bug
- `docs`: 文档更新
- `style`: 代码格式调整
- `refactor`: 代码重构
- `test`: 测试相关

### 代码规范
- 使用ESLint进行代码检查
- 遵循Vue 3 Composition API规范
- 统一的错误处理机制
- 完善的注释文档

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## 📞 联系方式

- 项目维护者: [您的姓名]
- 邮箱: <EMAIL>
- 项目地址: [GitHub仓库地址]

## 🙏 致谢

感谢所有为这个项目做出贡献的开发者！

---

**武汉总厂仓库管理系统** - 让仓库管理更简单、更高效！
